/**
 * Teacher Dashboard Page
 *
 * This page shows the dashboard for Teacher users.
 *
 * English: This page shows the Teacher dashboard with student registration and course mapping
 * Tanglish: Indha page Teacher-kku dashboard-a display pannum, student registration and course mapping-oda
 */

import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import DashboardLayout from '../dashboard/DashboardLayout';
import { userService } from '../../services/userService';
import { studentService } from '../../services/studentService';
import { courseService } from '../../services/courseService';
import { parentService } from '../../services/parentService';
import { authService } from '../../services/authService';

const TeacherDashboard = () => {
  const location = useLocation();

  // Get active section from URL parameters
  const searchParams = new URLSearchParams(location.search);
  const activeSection = searchParams.get('tab') || 'dashboard';

  // State for students
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // State for courses
  const [courses, setCourses] = useState([]);
  const [loadingCourses, setLoadingCourses] = useState(false);

  // State for parents
  const [parents, setParents] = useState([]);
  const [loadingParents, setLoadingParents] = useState(false);

  // State for user form (for student/parent registration)
  const [newUser, setNewUser] = useState({
    username: '',
    password: '',
    email: '',
    role: 'Student'
    // main_code will be automatically set from the teacher's main_code
  });

  // State for student form
  const [newStudent, setNewStudent] = useState({
    user_id: '',
    first_name: '',
    last_name: '',
    date_of_birth: '',
    address: '',
    phone: ''
    // main_code will be automatically set from the teacher's main_code
  });

  // State for parent form
  const [newParent, setNewParent] = useState({
    user_id: '',
    first_name: '',
    last_name: '',
    occupation: '',
    address: '',
    phone: ''
    // main_code will be automatically set from the teacher's main_code
  });

  // State for course form
  const [newCourse, setNewCourse] = useState({
    name: '',
    description: ''
    // main_code will be automatically set from the teacher's main_code
  });

  // State for course mapping form
  const [courseMapping, setCourseMapping] = useState({
    student_id: '',
    course_id: ''
  });

  // State for parent mapping form
  const [parentMapping, setParentMapping] = useState({
    parent_id: '',
    student_id: '',
    relationship: ''
  });

  // State for existing parent mappings
  const [existingParents, setExistingParents] = useState([]);

  /**
   * Load students from API
   *
   * English: This function loads all students from the API
   * Tanglish: Indha function API-la irundhu ella students-um load pannum
   */
  const loadStudents = async () => {
    try {
      setLoading(true);
      setError('');

      const data = await studentService.getStudents();
      setStudents(data.students || []);
    } catch (error) {
      setError(error.error || 'Failed to load students');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load courses from API
   *
   * English: This function loads all courses from the API
   * Tanglish: Indha function API-la irundhu ella courses-um load pannum
   */
  const loadCourses = async () => {
    try {
      setLoadingCourses(true);

      const data = await courseService.getCourses();
      setCourses(data.courses || []);
    } catch (error) {
      console.error('Failed to load courses:', error);
    } finally {
      setLoadingCourses(false);
    }
  };

  /**
   * Load parents from API
   *
   * English: This function loads all parents from the API
   * Tanglish: Indha function API-la irundhu ella parents-um load pannum
   */
  const loadParents = async () => {
    try {
      setLoadingParents(true);

      const data = await parentService.getParents();
      setParents(data.parents || []);
    } catch (error) {
      console.error('Failed to load parents:', error);
    } finally {
      setLoadingParents(false);
    }
  };

  /**
   * Handle user form input change
   *
   * @param {Event} e - Input change event
   *
   * English: This function handles changes to the user form inputs
   * Tanglish: Indha function user form inputs-la changes-a handle pannum
   */
  const handleUserInputChange = (e) => {
    const { name, value } = e.target;
    setNewUser({
      ...newUser,
      [name]: value
    });
  };

  /**
   * Handle student form input change
   *
   * @param {Event} e - Input change event
   *
   * English: This function handles changes to the student form inputs
   * Tanglish: Indha function student form inputs-la changes-a handle pannum
   */
  const handleStudentInputChange = (e) => {
    const { name, value } = e.target;
    setNewStudent({
      ...newStudent,
      [name]: value
    });
  };

  /**
   * Handle course form input change
   *
   * @param {Event} e - Input change event
   *
   * English: This function handles changes to the course form inputs
   * Tanglish: Indha function course form inputs-la changes-a handle pannum
   */
  const handleCourseInputChange = (e) => {
    const { name, value } = e.target;
    setNewCourse({
      ...newCourse,
      [name]: value
    });
  };

  /**
   * Handle course mapping form input change
   *
   * @param {Event} e - Input change event
   *
   * English: This function handles changes to the course mapping form inputs
   * Tanglish: Indha function course mapping form inputs-la changes-a handle pannum
   */
  const handleCourseMappingChange = (e) => {
    const { name, value } = e.target;
    setCourseMapping({
      ...courseMapping,
      [name]: value
    });
  };

  /**
   * Handle parent mapping form input change
   *
   * @param {Event} e - Input change event
   *
   * English: This function handles changes to the parent mapping form inputs
   * Tanglish: Indha function parent mapping form inputs-la changes-a handle pannum
   */
  const handleParentMappingChange = async (e) => {
    const { name, value } = e.target;
    setParentMapping({
      ...parentMapping,
      [name]: value
    });

    // If student_id is selected, fetch existing parents for this student
    if (name === 'student_id' && value) {
      try {
        setLoading(true);
        const data = await studentService.getStudentParents(value);
        setExistingParents(data.parents || []);

        // If student already has parents, show a message
        if (data.parents && data.parents.length > 0) {
          const parentNames = data.parents.map(p => `${p.first_name} ${p.last_name} (${p.relationship})`).join(', ');
          setError(`Note: This student already has the following parents mapped: ${parentNames}`);
        } else {
          setError('');
        }
      } catch (error) {
        console.error('Error fetching student parents:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  /**
   * Handle student registration form submission
   *
   * @param {Event} e - Form submit event
   *
   * English: This function handles student registration form submission
   * Tanglish: Indha function student registration form submit-a handle pannum
   */
  const handleStudentRegistration = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      // Get the teacher's main_code from localStorage
      const teacherMainCode = authService.getMainCode();

      if (!teacherMainCode) {
        setError('Main code not found. Please contact administrator.');
        setLoading(false);
        return;
      }

      // First register the user with the teacher's main_code
      const userData = await userService.registerUser({
        ...newUser,
        role: 'Student',
        main_code: teacherMainCode
      });

      // Then register the student with the user ID and teacher's main_code
      await studentService.registerStudent({
        ...newStudent,
        user_id: userData.user.id,
        main_code: teacherMainCode
      });

      // Reset forms
      setNewUser({
        username: '',
        password: '',
        email: '',
        role: 'Student'
      });

      setNewStudent({
        user_id: '',
        first_name: '',
        last_name: '',
        date_of_birth: '',
        address: '',
        phone: ''
      });

      // Show success message
      setError('Student registered successfully');

      // Reload students
      await loadStudents();
    } catch (error) {
      setError(error.error || 'Failed to register student');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle parent registration form submission
   *
   * @param {Event} e - Form submit event
   *
   * English: This function handles parent registration form submission
   * Tanglish: Indha function parent registration form submit-a handle pannum
   */
  const handleParentRegistration = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      // Get the teacher's main_code from localStorage
      const teacherMainCode = authService.getMainCode();

      if (!teacherMainCode) {
        setError('Main code not found. Please contact administrator.');
        setLoading(false);
        return;
      }

      // First register the user with the teacher's main_code
      const userData = await userService.registerUser({
        ...newUser,
        role: 'Parent',
        main_code: teacherMainCode
      });

      // Then register the parent with the user ID and teacher's main_code
      await parentService.registerParent({
        ...newParent,
        user_id: userData.user.id,
        main_code: teacherMainCode
      });

      // Reset forms
      setNewUser({
        username: '',
        password: '',
        email: '',
        role: 'Parent'
      });

      setNewParent({
        user_id: '',
        first_name: '',
        last_name: '',
        occupation: '',
        address: '',
        phone: ''
      });

      // Show success message
      setError('Parent registered successfully');

      // Reload parents
      await loadParents();
    } catch (error) {
      setError(error.error || 'Failed to register parent');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle course creation form submission
   *
   * @param {Event} e - Form submit event
   *
   * English: This function handles course creation form submission
   * Tanglish: Indha function course creation form submit-a handle pannum
   */
  const handleCourseCreation = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      // Get the teacher's main_code from localStorage
      const teacherMainCode = authService.getMainCode();

      if (!teacherMainCode) {
        setError('Main code not found. Please contact administrator.');
        setLoading(false);
        return;
      }

      // Create course with the teacher's main_code
      await courseService.createCourse({
        ...newCourse,
        main_code: teacherMainCode
      });

      // Reset form
      setNewCourse({
        name: '',
        description: ''
      });

      // Show success message
      setError('Course created successfully');

      // Reload courses
      await loadCourses();
    } catch (error) {
      setError(error.error || 'Failed to create course');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle course mapping form submission
   *
   * @param {Event} e - Form submit event
   *
   * English: This function handles course mapping form submission
   * Tanglish: Indha function course mapping form submit-a handle pannum
   */
  const handleCourseMapping = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      await courseService.mapStudentToCourse(
        courseMapping.student_id,
        courseMapping.course_id
      );

      // Reset form
      setCourseMapping({
        student_id: '',
        course_id: ''
      });

      // Show success message
      setError('Student mapped to course successfully');
    } catch (error) {
      setError(error.error || 'Failed to map student to course');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle parent mapping form submission
   *
   * @param {Event} e - Form submit event
   *
   * English: This function handles parent mapping form submission
   * Tanglish: Indha function parent mapping form submit-a handle pannum
   */
  const handleParentMapping = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      await studentService.mapParentToStudent(
        parentMapping.parent_id,
        parentMapping.student_id,
        parentMapping.relationship
      );

      // Reset form and existing parents
      setParentMapping({
        parent_id: '',
        student_id: '',
        relationship: ''
      });
      setExistingParents([]);

      // Show success message
      setError('Parent mapped to student successfully');
    } catch (error) {
      setError(error.error || 'Failed to map parent to student');
    } finally {
      setLoading(false);
    }
  };

  // Load data when component mounts or section changes
  useEffect(() => {
    if (activeSection === 'dashboard') {
      // Load all data for dashboard overview
      loadStudents();
      loadCourses();
      loadParents();
    } else if (activeSection === 'students') {
      loadStudents();
    } else if (activeSection === 'courses') {
      loadCourses();
      loadStudents();
    } else if (activeSection === 'parents') {
      loadParents();
      loadStudents();
    }
  }, [activeSection]);

  // Render dashboard overview
  const renderDashboard = () => (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Total Students</h3>
          <p className="text-3xl font-bold text-purple-600">{students.length}</p>
        </div>
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Total Courses</h3>
          <p className="text-3xl font-bold text-green-600">{courses.length}</p>
        </div>
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Total Parents</h3>
          <p className="text-3xl font-bold text-teal-600">{parents.length}</p>
        </div>
      </div>
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">Welcome to Teacher Dashboard</h3>
        <p className="text-gray-600">Use the sidebar navigation to manage students, courses, and parents.</p>
      </div>
    </div>
  );

  return (
    <DashboardLayout title="Teacher Dashboard">
      <div className="container mx-auto px-4 py-8">
        {activeSection === 'dashboard' && renderDashboard()}

        {activeSection === 'students' && (
        <div>
          <div className="card mb-6">
            <h2 className="text-xl font-semibold mb-4">Register New Student</h2>

            {error && (
              <div className={`alert ${error.includes('successfully') ? 'alert-success' : 'alert-danger'}`} role="alert">
                {error}
              </div>
            )}

            <form onSubmit={handleStudentRegistration}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-group">
                  <label htmlFor="username" className="form-label">Username</label>
                  <input
                    type="text"
                    className="form-input"
                    id="username"
                    name="username"
                    value={newUser.username}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="password" className="form-label">Password</label>
                  <input
                    type="password"
                    className="form-input"
                    id="password"
                    name="password"
                    value={newUser.password}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="email" className="form-label">Email</label>
                  <input
                    type="email"
                    className="form-input"
                    id="email"
                    name="email"
                    value={newUser.email}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="first_name" className="form-label">First Name</label>
                  <input
                    type="text"
                    className="form-input"
                    id="first_name"
                    name="first_name"
                    value={newStudent.first_name}
                    onChange={handleStudentInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="last_name" className="form-label">Last Name</label>
                  <input
                    type="text"
                    className="form-input"
                    id="last_name"
                    name="last_name"
                    value={newStudent.last_name}
                    onChange={handleStudentInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="date_of_birth" className="form-label">Date of Birth</label>
                  <input
                    type="date"
                    className="form-input"
                    id="date_of_birth"
                    name="date_of_birth"
                    value={newStudent.date_of_birth}
                    onChange={handleStudentInputChange}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="address" className="form-label">Address</label>
                  <input
                    type="text"
                    className="form-input"
                    id="address"
                    name="address"
                    value={newStudent.address}
                    onChange={handleStudentInputChange}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="phone" className="form-label">Phone</label>
                  <input
                    type="text"
                    className="form-input"
                    id="phone"
                    name="phone"
                    value={newStudent.phone}
                    onChange={handleStudentInputChange}
                  />
                </div>
              </div>

              <button type="submit" className="btn btn-primary mt-4" disabled={loading}>
                {loading ? 'Registering...' : 'Register Student'}
              </button>
            </form>
          </div>

          <div className="card">
            <h2 className="text-xl font-semibold mb-4">All Students</h2>

            {loading ? (
              <p>Loading students...</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white">
                  <thead>
                    <tr>
                      <th className="py-2 px-4 border-b">ID</th>
                      <th className="py-2 px-4 border-b">User ID</th>
                      <th className="py-2 px-4 border-b">First Name</th>
                      <th className="py-2 px-4 border-b">Last Name</th>
                      <th className="py-2 px-4 border-b">Date of Birth</th>
                      <th className="py-2 px-4 border-b">Phone</th>
                    </tr>
                  </thead>
                  <tbody>
                    {students.map((student) => (
                      <tr key={student.id}>
                        <td className="py-2 px-4 border-b">{student.id}</td>
                        <td className="py-2 px-4 border-b">{student.user_id}</td>
                        <td className="py-2 px-4 border-b">{student.first_name}</td>
                        <td className="py-2 px-4 border-b">{student.last_name}</td>
                        <td className="py-2 px-4 border-b">{student.date_of_birth}</td>
                        <td className="py-2 px-4 border-b">{student.phone}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
        )}

        {activeSection === 'courses' && (
          <div className="space-y-8">
            <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-indigo-500">
              <h2 className="text-2xl font-bold mb-4 text-indigo-700">Create New Course</h2>

              {error && (
                <div className={`p-4 mb-4 rounded-md ${error.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
                  {error}
                </div>
              )}

              <form onSubmit={handleCourseCreation}>
                <div className="grid grid-cols-1 gap-4">
                  <div className="form-group">
                    <label htmlFor="name" className="form-label text-gray-700 font-medium">Course Name</label>
                    <input
                      type="text"
                      className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                      id="name"
                      name="name"
                      value={newCourse.name}
                      onChange={handleCourseInputChange}
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="description" className="form-label text-gray-700 font-medium">Description</label>
                    <textarea
                      className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                      id="description"
                      name="description"
                      value={newCourse.description}
                      onChange={handleCourseInputChange}
                      rows="3"
                    ></textarea>
                  </div>
                </div>

                <button type="submit" className="mt-4 px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loading}>
                  {loading ? 'Creating...' : 'Create Course'}
                </button>
              </form>
            </div>

            <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-purple-500">
              <h2 className="text-2xl font-bold mb-4 text-purple-700">Map Student to Course</h2>

              {error && (
                <div className={`p-4 mb-4 rounded-md ${error.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
                  {error}
                </div>
              )}

              <form onSubmit={handleCourseMapping}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="form-group">
                    <label htmlFor="student_id" className="form-label text-gray-700 font-medium">Student</label>
                    <select
                      className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                      id="student_id"
                      name="student_id"
                      value={courseMapping.student_id}
                      onChange={handleCourseMappingChange}
                      required
                    >
                      <option value="">Select Student</option>
                      {students.map((student) => (
                        <option key={student.id} value={student.id}>
                          {student.first_name} {student.last_name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label htmlFor="course_id" className="form-label text-gray-700 font-medium">Course</label>
                    <select
                      className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                      id="course_id"
                      name="course_id"
                      value={courseMapping.course_id}
                      onChange={handleCourseMappingChange}
                      required
                    >
                      <option value="">Select Course</option>
                      {courses.map((course) => (
                        <option key={course.id} value={course.id}>
                          {course.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <button type="submit" className="mt-4 px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loading}>
                  {loading ? 'Mapping...' : 'Map Student to Course'}
                </button>
              </form>
            </div>

            <div className="card bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-bold mb-4 text-purple-700">All Courses</h2>

              {loadingCourses ? (
                <p className="text-gray-600">Loading courses...</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {courses.map((course) => (
                        <tr key={course.id} className="hover:bg-gray-50">
                          <td className="py-3 px-4 text-sm text-gray-900">{course.id}</td>
                          <td className="py-3 px-4 text-sm text-gray-900 font-medium">{course.name}</td>
                          <td className="py-3 px-4 text-sm text-gray-500">{course.description}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}


        {activeSection === 'parents' && (
          <div className="space-y-8">
            <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-teal-500">
              <h2 className="text-2xl font-bold mb-4 text-teal-700">Register New Parent</h2>

            {error && (
              <div className={`p-4 mb-4 rounded-md ${error.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
                {error}
              </div>
            )}

            <form onSubmit={handleParentRegistration}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-group">
                  <label htmlFor="username" className="form-label text-gray-700 font-medium">Username</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
                    id="username"
                    name="username"
                    value={newUser.username}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="password" className="form-label text-gray-700 font-medium">Password</label>
                  <input
                    type="password"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
                    id="password"
                    name="password"
                    value={newUser.password}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="email" className="form-label text-gray-700 font-medium">Email</label>
                  <input
                    type="email"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
                    id="email"
                    name="email"
                    value={newUser.email}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="first_name" className="form-label text-gray-700 font-medium">First Name</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
                    id="first_name"
                    name="first_name"
                    value={newParent.first_name}
                    onChange={(e) => setNewParent({...newParent, first_name: e.target.value})}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="last_name" className="form-label text-gray-700 font-medium">Last Name</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
                    id="last_name"
                    name="last_name"
                    value={newParent.last_name}
                    onChange={(e) => setNewParent({...newParent, last_name: e.target.value})}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="occupation" className="form-label text-gray-700 font-medium">Occupation</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
                    id="occupation"
                    name="occupation"
                    value={newParent.occupation}
                    onChange={(e) => setNewParent({...newParent, occupation: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="address" className="form-label text-gray-700 font-medium">Address</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
                    id="address"
                    name="address"
                    value={newParent.address}
                    onChange={(e) => setNewParent({...newParent, address: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="phone" className="form-label text-gray-700 font-medium">Phone</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
                    id="phone"
                    name="phone"
                    value={newParent.phone}
                    onChange={(e) => setNewParent({...newParent, phone: e.target.value})}
                  />
                </div>
              </div>

              <button type="submit" className="mt-4 px-6 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loading}>
                {loading ? 'Registering...' : 'Register Parent'}
              </button>
            </form>
          </div>

          <div className="card bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-teal-700">All Parents</h2>

            {loadingParents ? (
              <p className="text-gray-600">Loading parents...</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User ID</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">First Name</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Name</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {parents.map((parent) => (
                      <tr key={parent.id} className="hover:bg-gray-50">
                        <td className="py-3 px-4 text-sm text-gray-900">{parent.id}</td>
                        <td className="py-3 px-4 text-sm text-gray-900">{parent.user_id}</td>
                        <td className="py-3 px-4 text-sm text-gray-900 font-medium">{parent.first_name}</td>
                        <td className="py-3 px-4 text-sm text-gray-900 font-medium">{parent.last_name}</td>
                        <td className="py-3 px-4 text-sm text-gray-500">{parent.phone}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-amber-500">
              <h2 className="text-2xl font-bold mb-4 text-amber-700">Map Parent to Student</h2>

              {error && (
                <div className={`p-4 mb-4 rounded-md ${error.includes('successfully') ? 'bg-green-100 text-green-700' : error.includes('Note:') ? 'bg-blue-100 text-blue-700' : 'bg-red-100 text-red-700'}`} role="alert">
                  {error}
                </div>
              )}

              <form onSubmit={handleParentMapping}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="form-group">
                    <label htmlFor="student_id" className="form-label text-gray-700 font-medium">Student</label>
                    <select
                      className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-200 focus:ring-opacity-50"
                      id="student_id"
                      name="student_id"
                      value={parentMapping.student_id}
                      onChange={handleParentMappingChange}
                      required
                    >
                      <option value="">Select Student</option>
                      {students.map((student) => (
                        <option key={student.id} value={student.id}>
                          {student.first_name} {student.last_name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label htmlFor="parent_id" className="form-label text-gray-700 font-medium">Parent</label>
                    <select
                      className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-200 focus:ring-opacity-50"
                      id="parent_id"
                      name="parent_id"
                      value={parentMapping.parent_id}
                      onChange={handleParentMappingChange}
                      required
                    >
                      <option value="">Select Parent</option>
                      {parents.map((parent) => (
                        <option key={parent.id} value={parent.id}>
                          {parent.first_name} {parent.last_name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label htmlFor="relationship" className="form-label text-gray-700 font-medium">Relationship</label>
                    <select
                      className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-200 focus:ring-opacity-50"
                      id="relationship"
                      name="relationship"
                      value={parentMapping.relationship}
                      onChange={handleParentMappingChange}
                      required
                    >
                      <option value="">Select Relationship</option>
                      <option value="Father">Father</option>
                      <option value="Mother">Mother</option>
                      <option value="Guardian">Guardian</option>
                    </select>
                  </div>
                </div>

                {existingParents.length > 0 && (
                  <div className="mt-4 p-4 bg-blue-50 rounded-md border border-blue-200">
                    <h3 className="text-lg font-semibold text-blue-700 mb-2">Existing Parents for Selected Student</h3>
                    <ul className="list-disc pl-5 space-y-1">
                      {existingParents.map((parent) => (
                        <li key={parent.id} className="text-blue-600">
                          {parent.first_name} {parent.last_name} - <span className="font-medium">{parent.relationship}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <button type="submit" className="mt-4 px-6 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loading}>
                  {loading ? 'Mapping...' : 'Map Parent to Student'}
                </button>
              </form>
            </div>
          </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default TeacherDashboard;
