/**
 * Application Configuration
 *
 * This file centralizes all environment variables and configuration settings.
 * It provides a single source of truth for application configuration.
 */

// API URLs
const API = {
  BASE: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  LOGIN: import.meta.env.VITE_LOGIN_URL || 'http://localhost:5000/api/auth/login',
  FORGET: import.meta.env.VITE_FORGET_URL || 'http://localhost:5000/api/auth/forgot-password',

  // Service-specific URLs (for backward compatibility)
  AUTH: import.meta.env.VITE_API_URL_AUTH || 'http://localhost:5000/api/auth',
  USERS: import.meta.env.VITE_API_URL_USERS || 'http://localhost:5001/api/users',
  STUDENTS: import.meta.env.VITE_API_URL_STUDENTS || 'http://localhost:5002/api/students',
  COURSES: import.meta.env.VITE_API_URL_COURSES || 'http://localhost:5003/api/courses',
  PARENTS: import.meta.env.VITE_API_URL_PARENTS || 'http://localhost:5004/api/parents',
};

// Environment settings
const ENV = {
  NAME: import.meta.env.VITE_ENV || 'development',
  IS_PROD: import.meta.env.VITE_ENV === 'production',
  IS_DEV: import.meta.env.VITE_ENV === 'development' || import.meta.env.VITE_ENV === 'local',
  DEBUG: import.meta.env.VITE_DEBUG === 'true',
  LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL || 'info',
};

// Authentication settings
const AUTH = {
  TOKEN_KEY: 'token',
  USER_KEY: 'user',
  MAIN_CODE_KEY: 'main_code',
};

// Application settings
const APP = {
  NAME: 'Pandra2',
  VERSION: '1.0.0',
};

// Export all configuration
const config = {
  API,
  ENV,
  AUTH,
  APP,
};

export default config;
